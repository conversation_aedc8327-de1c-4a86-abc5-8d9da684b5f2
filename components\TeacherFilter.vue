<template>
  <aside class="filters">
    <client-only>
      <v-form @submit.prevent="submitFormHandler">
        <div class="filters-head">
          <div class="filters-head-close d-md-none">
            <div
              class="filters-head-close-icon"
              @click="closeTeacherFilterClickHandler"
            >
              <svg width="34" height="34" viewBox="0 0 34 34">
                <use
                  :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#close-big`"
                ></use>
              </svg>
            </div>
          </div>
          <div class="filters-head-title">
            <span class="d-none d-md-inline-block">{{
              $t('find_your_teacher')
            }}</span>
            <span class="d-md-none">{{ $t('filters') }}</span>
          </div>
          <div class="filters-head-clear" @click="resetAllClickHandler">
            {{ $t('clear_all') }}
          </div>
        </div>
        <div class="filters-content">
          <v-expansion-panels
            :value="panel"
            accordion
            flat
            @change="setActivePanel"
          >
            <v-expansion-panel v-if="languages">
              <v-expansion-panel-header disable-icon-rotate>
                <div>{{ $t('language') }}</div>

                <template #actions>
                  <template v-if="isOpenedPanel(0)">
                    <v-img
                      :src="require('~/assets/images/chevron-gradient.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                  <template v-else>
                    <v-img
                      :src="require('~/assets/images/chevron-w.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-row no-gutters>
                  <v-col class="col-12">
                    <div class="autocomplete selected-language">
                      <client-only>
                        <v-autocomplete
                          ref="languageAutocomplete"
                          v-model="selectedLanguage"
                          :items="languages"
                          item-text="name"
                          dense
                          filled
                          dark
                          hide-selected
                          hide-no-data
                          return-object
                          hide-details
                          :placeholder="$t('choose_language')"
                          attach=".selected-language"
                          :menu-props="{
                            dark: true,
                            bottom: true,
                            offsetY: true,
                            absolute: false,
                            nudgeBottom: -5,
                            contentClass:
                              'filters-dropdown-list l-scroll l-scroll--grey',
                            maxHeight: 192,
                          }"
                        >
                          <template #item="{ item }">
                            <v-img
                              :src="
                                require(`~/assets/images/flags/${item.isoCode}.svg`)
                              "
                              height="28"
                              width="28"
                              class="icon"
                              eager
                            ></v-img>
                            <div class="text">{{ item.name }}</div>
                          </template>
                        </v-autocomplete>
                      </client-only>
                    </div>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
              <v-row v-if="languageChip" no-gutters>
                <v-col class="col-12">
                  <div class="chips">
                    <l-chip
                      class="mt-2"
                      :label="languageChip.name"
                      @click:close="resetLanguage"
                    ></l-chip>
                  </div>
                </v-col>
              </v-row>
            </v-expansion-panel>
            <v-expansion-panel v-if="motivations && motivations.length">
              <v-expansion-panel-header disable-icon-rotate>
                <div>{{ $t('my_motivation') }}</div>

                <template #actions>
                  <template v-if="isOpenedPanel(1)">
                    <v-img
                      :src="require('~/assets/images/chevron-gradient.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                  <template v-else>
                    <v-img
                      :src="require('~/assets/images/chevron-w.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-radio-group v-model="selectedMotivation" hide-details>
                  <v-row no-gutters class="mb-2">
                    <v-col
                      v-for="motivation in motivations"
                      :key="motivation.id"
                      class="col-auto"
                    >
                      <div
                        :class="[
                          'checkbox checkbox--motivation pr-1 pb-2',
                          {
                            'checkbox--checked':
                              selectedMotivation &&
                              selectedMotivation.id === motivation.id,
                          },
                        ]"
                      >
                        <div v-if="motivation.icon" class="checkbox-icon">
                          <svg width="16" height="16" viewBox="0 0 16 16">
                            <use
                              :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#${
                                motivation.icon
                              }`"
                            ></use>
                          </svg>
                        </div>
                        <v-radio
                          :label="motivation.motivationName"
                          dark
                          class="l-radio-button"
                          :ripple="false"
                          :value="motivation"
                        ></v-radio>
                      </div>
                    </v-col>
                  </v-row>
                </v-radio-group>
                <template v-if="specialities && specialities.length">
                  <v-row no-gutters>
                    <v-col
                      v-for="speciality in specialities"
                      :key="speciality.id"
                      class="col-6"
                    >
                      <div class="checkbox">
                        <v-checkbox
                          v-model="selectedSpecialities"
                          :value="speciality"
                          class="l-checkbox"
                          :label="speciality.name"
                          dark
                          hide-details
                          :ripple="false"
                        ></v-checkbox>
                      </div>
                    </v-col>
                  </v-row>
                </template>
              </v-expansion-panel-content>
              <v-row v-if="motivationChip || specialityChips.length" no-gutters>
                <v-col class="col-12">
                  <div class="chips">
                    <template v-if="motivationChip">
                      <l-chip
                        :icon="motivationChip.icon"
                        :label="motivationChip.motivationName"
                        class="mt-2"
                        @click:close="resetMotivation"
                      ></l-chip>
                    </template>

                    <template v-if="specialityChips.length">
                      <template v-for="activeSpeciality in specialityChips">
                        <l-chip
                          v-if="activeSpeciality.isPublish"
                          :key="activeSpeciality.id"
                          :label="activeSpeciality.name"
                          class="mt-2"
                          @click:close="resetSpeciality(activeSpeciality)"
                        ></l-chip>
                      </template>
                    </template>
                  </div>
                </v-col>
              </v-row>
            </v-expansion-panel>
            <v-expansion-panel v-if="proficiencyLevels">
              <v-expansion-panel-header disable-icon-rotate>
                <div>{{ $t('my_level') }}</div>

                <template #actions>
                  <template v-if="isOpenedPanel(2)">
                    <v-img
                      :src="require('~/assets/images/chevron-gradient.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                  <template v-else>
                    <v-img
                      :src="require('~/assets/images/chevron-w.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-row no-gutters>
                  <v-col class="col-12">
                    <div class="radiobutton">
                      <v-radio-group
                        v-model="selectedProficiencyLevel"
                        hide-details
                      >
                        <v-radio
                          v-for="level in proficiencyLevels"
                          :key="level.id"
                          :label="level.name"
                          class="l-radio-button"
                          dark
                          :ripple="false"
                          :value="level"
                        ></v-radio>
                      </v-radio-group>
                    </div>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
              <v-row v-if="proficiencyLevelChip" no-gutters>
                <v-col class="col-12">
                  <div class="chips">
                    <l-chip
                      :label="proficiencyLevelChip.name"
                      class="mt-2"
                      @click:close="resetLevel"
                    ></l-chip>
                  </div>
                </v-col>
              </v-row>
            </v-expansion-panel>
            <v-expansion-panel v-if="teacherPreferences">
              <v-expansion-panel-header disable-icon-rotate>
                <div>{{ $t('i_prefer_teacher_who') }}</div>

                <template #actions>
                  <template v-if="isOpenedPanel(3)">
                    <v-img
                      :src="require('~/assets/images/chevron-gradient.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                  <template v-else>
                    <v-img
                      :src="require('~/assets/images/chevron-w.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-row no-gutters>
                  <v-col class="col-12">
                    <div class="radiobutton">
                      <v-radio-group
                        v-model="selectedTeacherPreference"
                        hide-details
                      >
                        <v-radio
                          v-for="teacherPreference in teacherPreferences"
                          :key="teacherPreference.id"
                          :label="teacherPreference.name"
                          class="l-radio-button"
                          dark
                          :ripple="false"
                          :value="teacherPreference"
                        ></v-radio>
                      </v-radio-group>
                    </div>
                  </v-col>
                </v-row>
                <v-row
                  v-if="
                    selectedTeacherPreference &&
                    selectedTeacherPreference.id === 2
                  "
                  no-gutters
                  class="mt-1"
                >
                  <v-col class="col-12">
                    <div class="autocomplete teacher-preference-language">
                      <v-autocomplete
                        ref="preferenceLanguageAutocomplete"
                        v-model="selectedTeacherPreferenceLanguage"
                        :items="languages"
                        item-text="name"
                        dense
                        filled
                        dark
                        hide-selected
                        hide-no-data
                        return-object
                        hide-details
                        :placeholder="$t('choose_language')"
                        attach=".teacher-preference-language"
                        :menu-props="{
                          dark: true,
                          bottom: true,
                          offsetY: true,
                          absolute: false,
                          nudgeBottom: -5,
                          contentClass:
                            'filters-dropdown-list l-scroll l-scroll--grey',
                          maxHeight: 205,
                        }"
                      >
                        <template #item="{ item }">
                          <v-img
                            :src="
                              require(`~/assets/images/flags/${item.isoCode}.svg`)
                            "
                            height="28"
                            width="28"
                            class="icon"
                            eager
                          ></v-img>
                          <div class="text">{{ item.name }}</div>
                        </template>
                      </v-autocomplete>
                    </div>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
              <v-row
                v-if="teacherPreferenceChip && teacherPreferenceChip.id === 1"
                no-gutters
              >
                <v-col class="col-12">
                  <div class="chips">
                    <l-chip
                      :label="$t('native_speaker')"
                      class="mt-2"
                      @click:close="resetTeacherPreference"
                    ></l-chip>
                  </div>
                </v-col>
              </v-row>
              <v-row v-else-if="teacherMatchLanguageChip" no-gutters>
                <v-col class="col-12">
                  <div class="chips">
                    <l-chip
                      :label="`${$t('also_speaks')} ${
                        teacherMatchLanguageChip.name
                      }`"
                      class="mt-2"
                      @click:close="resetTeacherPreference"
                    ></l-chip>
                  </div>
                </v-col>
              </v-row>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header disable-icon-rotate>
                <div>{{ $t('days_per_week') }}</div>

                <template #actions>
                  <template v-if="isOpenedPanel(4)">
                    <v-img
                      :src="require('~/assets/images/chevron-gradient.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                  <template v-else>
                    <v-img
                      :src="require('~/assets/images/chevron-w.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-row no-gutters>
                  <v-col class="col-6">
                    <div class="checkbox">
                      <v-checkbox
                        v-model="isSelectedAllDays"
                        :label="$t('all')"
                        class="l-checkbox"
                        dark
                        hide-details
                        :ripple="false"
                        @change="allDaysChangeHandler"
                      ></v-checkbox>
                    </div>
                  </v-col>
                  <v-col v-for="day in days" :key="day.id" class="col-12">
                    <div class="checkbox">
                      <v-checkbox
                        v-model="selectedDays"
                        :value="day"
                        class="l-checkbox"
                        :label="$t(day.name)"
                        dark
                        hide-details
                        :ripple="false"
                      ></v-checkbox>
                    </div>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
              <v-row v-if="dateChips.length" no-gutters>
                <v-col class="col-12">
                  <div class="chips">
                    <l-chip
                      v-for="activeDate in dateChips"
                      :key="activeDate.id"
                      :label="$t(activeDate.name)"
                      class="mt-2"
                      @click:close="resetDay(activeDate)"
                    ></l-chip>
                  </div>
                </v-col>
              </v-row>
            </v-expansion-panel>
            <v-expansion-panel>
              <v-expansion-panel-header disable-icon-rotate>
                <div>{{ $t('time_of_day') }}</div>

                <template #actions>
                  <template v-if="isOpenedPanel(5)">
                    <v-img
                      :src="require('~/assets/images/chevron-gradient.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                  <template v-else>
                    <v-img
                      :src="require('~/assets/images/chevron-w.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <v-row no-gutters>
                  <v-col class="col-6">
                    <div class="checkbox">
                      <v-checkbox
                        v-model="isSelectedAllTimes"
                        :label="$t('all')"
                        class="l-checkbox"
                        dark
                        hide-details
                        :ripple="false"
                        @change="allTimesChangeHandler"
                      ></v-checkbox>
                    </div>
                  </v-col>
                  <v-col v-for="time in times" :key="time.id" class="col-12">
                    <div class="checkbox">
                      <v-checkbox
                        v-model="selectedTimes"
                        :value="time"
                        class="l-checkbox"
                        dark
                        hide-details
                        :ripple="false"
                      >
                        <template #label>
                          <div
                            v-if="time.image"
                            class="label-icon label-icon--time"
                          >
                            <svg width="16" height="16" viewBox="0 0 16 16">
                              <use
                                :xlink:href="`${require('~/assets/images/icon-sprite.svg')}#${
                                  time.image
                                }`"
                              ></use>
                            </svg>
                          </div>
                          {{ $t(time.name) }}&#160;
                          <span class="checkbox-period">
                            {{ time.period }}
                          </span>
                        </template>
                      </v-checkbox>
                    </div>
                  </v-col>
                </v-row>
                <v-row no-gutters>
                  <v-col class="col-12">
                    <lesson-time-notice
                      class="filters-notice body-2"
                      dark
                    ></lesson-time-notice>
                  </v-col>
                </v-row>
              </v-expansion-panel-content>
              <v-row v-if="timeChips.length" no-gutters>
                <v-col class="col-12">
                  <div class="chips">
                    <l-chip
                      v-for="activeTime in timeChips"
                      :key="activeTime.id"
                      :label="$t(activeTime.name)"
                      class="mt-2"
                      :icon="activeTime.image"
                      @click:close="resetTime(activeTime)"
                    ></l-chip>
                  </div>
                </v-col>
              </v-row>
            </v-expansion-panel>
            <v-expansion-panel v-if="!isUserLogged && currencies">
              <v-expansion-panel-header disable-icon-rotate>
                <div>{{ $t('currency') }}</div>

                <template #actions>
                  <template v-if="isOpenedPanel(6)">
                    <v-img
                      :src="require('~/assets/images/chevron-gradient.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                  <template v-else>
                    <v-img
                      :src="require('~/assets/images/chevron-w.svg')"
                      width="16"
                      height="16"
                    ></v-img>
                  </template>
                </template>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
                <div class="radiobutton">
                  <v-radio-group v-model="selectedCurrency" hide-details>
                    <v-row no-gutters>
                      <v-col
                        v-for="currency in currencies"
                        :key="currency.id"
                        class="col-6 mb-1"
                      >
                        <v-radio
                          :label="currency.isoCode"
                          class="l-radio-button"
                          dark
                          :ripple="false"
                          :value="currency"
                        ></v-radio>
                      </v-col>
                    </v-row>
                  </v-radio-group>
                </div>
              </v-expansion-panel-content>
              <v-row v-if="currencyChip && !isUserLogged" no-gutters>
                <v-col class="col-12">
                  <div class="chips">
                    <l-chip
                      :item="currencyChip"
                      class="mt-2"
                      :label="currencyChip.isoCode"
                      @click:close="resetCurrency"
                    ></l-chip>
                  </div>
                </v-col>
              </v-row>
            </v-expansion-panel>
          </v-expansion-panels>
        </div>
        <div class="filters-bottom d-md-none">
          <v-btn
            width="100%"
            large
            color="primary"
            class="text-uppercase"
            @click="closeTeacherFilterClickHandler"
          >
            {{ $t('go') }}!
          </v-btn>
        </div>
      </v-form>
    </client-only>
  </aside>
</template>

<script>
import LChip from '~/components/LChip'
import LessonTimeNotice from '~/components/LessonTimeNotice'

export default {
  name: 'TeacherFilter',
  components: { LChip, LessonTimeNotice },
  data() {
    return {
      panel: 0,
      isSelectedAllTimesProxy: false,
      isSelectedAllDaysProxy: false,
    }
  },
  computed: {
    languageChip() {
      return this.$store.getters['teacher_filter/languageChip']
    },
    motivationChip() {
      return this.$store.getters['teacher_filter/motivationChip']
    },
    specialityChips() {
      return this.$store.getters['teacher_filter/specialityChips']
    },
    proficiencyLevelChip() {
      return this.$store.getters['teacher_filter/proficiencyLevelChip']
    },
    teacherPreferenceChip() {
      return this.$store.getters['teacher_filter/teacherPreferenceChip']
    },
    teacherMatchLanguageChip() {
      return this.$store.getters['teacher_filter/teacherMatchLanguageChip']
    },
    dateChips() {
      return this.$store.getters['teacher_filter/dateChips']
    },
    timeChips() {
      return this.$store.getters['teacher_filter/timeChips']
    },
    currencyChip() {
      return this.$store.getters['teacher_filter/currencyChip']
    },
    isUserLogged() {
      return this.$store.getters['user/isUserLogged']
    },
    filters() {
      return this.$store.state.teacher_filter.filters
    },
    languages() {
      return this.filters?.languages
        .filter((item) => item.uiAvailable)
        .sort((a, b) => a.name.localeCompare(b.name, this.$i18n.locale))
    },
    motivations() {
      return this.filters?.motivations
    },
    specialities() {
      return this.$store.getters['teacher_filter/publishSpecialities']
    },
    proficiencyLevels() {
      return this.filters?.proficiencyLevels
    },
    teacherPreferences() {
      return this.filters?.teacherPreference
    },
    days() {
      return this.$store.getters['teacher_filter/days']
    },
    times() {
      return this.$store.getters['teacher_filter/times']
    },
    currencies() {
      return this.filters?.currencies
    },
    selectedLanguage: {
      get() {
        return this.$store.getters['teacher_filter/selectedLanguage']
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_LANGUAGE', {
          language: item,
        })
        this.submitFormHandler()
      },
    },
    selectedSpecialities: {
      get() {
        return this.$store.getters['teacher_filter/selectedSpecialities']
      },
      set(items) {
        this.$store.commit('teacher_filter/SET_SELECTED_SPECIALITIES', {
          specialities: items,
        })
        this.submitFormHandler()
      },
    },
    selectedMotivation: {
      get() {
        return this.$store.getters['teacher_filter/selectedMotivation']
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_MOTIVATION', {
          motivation: item,
        })
        this.submitFormHandler()
      },
    },
    selectedDays: {
      get() {
        return this.$store.getters['teacher_filter/selectedDays']
      },
      set(items) {
        this.$store.commit('teacher_filter/SET_SELECTED_DAYS', { dates: items })
        this.submitFormHandler()
      },
    },
    selectedTimes: {
      get() {
        return this.$store.getters['teacher_filter/selectedTimes']
      },
      set(items) {
        this.$store.commit('teacher_filter/SET_SELECTED_TIMES', {
          times: items,
        })
        this.submitFormHandler()
      },
    },
    selectedProficiencyLevel: {
      get() {
        return this.$store.getters['teacher_filter/selectedProficiencyLevel']
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_PROFICIENCY_LEVEL', {
          proficiencyLevel: item,
        })
        this.submitFormHandler()
      },
    },
    selectedTeacherPreference: {
      get() {
        return this.$store.getters['teacher_filter/selectedTeacherPreference']
      },
      set(item) {
        this.$store.commit('teacher_filter/SET_SELECTED_TEACHER_PREFERENCE', {
          teacherPreference: item,
        })

        if (item.id === 2) {
          this.openLanguageMenu()
        } else {
          this.$store.commit(
            'teacher_filter/RESET_SELECTED_TEACHER_PREFERENCE_LANGUAGE'
          )
          this.submitFormHandler()
        }
      },
    },
    selectedTeacherPreferenceLanguage: {
      get() {
        return this.$store.getters[
          'teacher_filter/selectedTeacherPreferenceLanguage'
        ]
      },
      set(item) {
        this.$store.commit(
          'teacher_filter/SET_SELECTED_TEACHER_PREFERENCE_LANGUAGE',
          { teacherPreferenceLanguage: item }
        )
        this.submitFormHandler()
      },
    },
    selectedCurrency: {
      get() {
        const { id } = this.$store.state.currency.item

        return this.filters.currencies.find((item) => item.id === id)
      },
      set(item) {
        this.$store.dispatch('currency/setItem', { item })
        this.submitFormHandler()
      },
    },
    selectedFeedbackTag() {
      return this.$store.getters['teacher_filter/selectedFeedbackTag']
    },
    searchQuery() {
      return this.$store.getters['teacher_filter/searchQuery']
    },
    selectedSorting() {
      return this.$store.getters['teacher_filter/selectedSorting']
    },
    needUpdateTeachers() {
      return this.$store.state.teacher_filter.needUpdateTeachers
    },
    isSelectedAllDays: {
      get() {
        return this.isSelectedAllDaysProxy
      },
      set(value) {
        this.isSelectedAllDaysProxy = value
      },
    },
    isSelectedAllTimes: {
      get() {
        return this.isSelectedAllTimesProxy
      },
      set(value) {
        this.isSelectedAllTimesProxy = value
      },
    },
    isShownTeacherFilter() {
      return this.$store.state.isShownTeacherFilter
    },
  },
  watch: {
    needUpdateTeachers(newValue, oldValue) {
      if (newValue) {
        this.submitFormHandler()
      }
    },
    isShownTeacherFilter(newValue, oldValue) {
      if (newValue) {
        this.openLanguageMenu()
      }
    },
  },
  beforeMount() {
    const activeFilterPanel = window.sessionStorage.getItem(
      'active-filter-panel'
    )

    if (activeFilterPanel) {
      this.panel = +activeFilterPanel
    } else {
      window.sessionStorage.setItem('active-filter-panel', '0')
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.isSelectedAllDays = this.selectedDays.length === this.days.length
      this.isSelectedAllTimes = this.selectedTimes.length === this.times.length

      if (this.$vuetify.breakpoint.mdAndUp) {
        this.openLanguageMenu()
      }

      this.$emit('filters-loaded')
    })
  },
  methods: {
    openLanguageMenu() {
      window.setTimeout(() => {
        if (this.panel === 0 && !this.selectedLanguage) {
          this.$refs.languageAutocomplete?.focus()
          this.$refs.languageAutocomplete?.activateMenu()
        }

        if (
          this.panel === 3 &&
          this.selectedTeacherPreference.id === 2 &&
          !this.selectedTeacherPreferenceLanguage
        ) {
          this.$refs.preferenceLanguageAutocomplete?.focus()
          this.$refs.preferenceLanguageAutocomplete?.activateMenu()
        }
      }, 100)
    },
    setActivePanel(id) {
      this.panel = id

      if (id !== undefined) {
        this.openLanguageMenu()
        window.sessionStorage.setItem('active-filter-panel', id)
      } else {
        window.sessionStorage.removeItem('active-filter-panel')
      }
    },
    isOpenedPanel(id) {
      return +this.panel === id
    },
    allDaysChangeHandler(e) {
      if (e) {
        this.selectedDays = this.days
      } else {
        this.resetDays()
      }
    },
    allTimesChangeHandler(e) {
      if (e) {
        this.selectedTimes = this.times
      } else {
        this.resetTimes()
      }
    },
    resetLanguage() {
      this.$store.commit('teacher_filter/RESET_SELECTED_LANGUAGE')
      this.submitFormHandler()
    },
    resetDays() {
      this.$store.commit('teacher_filter/RESET_SELECTED_DAYS')
      this.submitFormHandler()
    },
    resetTimes() {
      this.$store.commit('teacher_filter/RESET_SELECTED_TIMES')
      this.submitFormHandler()
    },
    resetSpeciality(item) {
      this.$store.commit('teacher_filter/UPDATE_SELECTED_SPECIALITIES', item)
      this.submitFormHandler()
    },
    resetMotivation() {
      this.$store.commit('teacher_filter/RESET_SELECTED_MOTIVATION')
      this.submitFormHandler()
    },
    resetTeacherPreference() {
      this.$store.commit('teacher_filter/RESET_SELECTED_TEACHER_PREFERENCE')
      this.submitFormHandler()
    },
    resetDay(item) {
      this.$store.commit('teacher_filter/UPDATE_SELECTED_DAYS', item)
      this.submitFormHandler()
    },
    resetTime(item) {
      this.$store.commit('teacher_filter/UPDATE_SELECTED_TIMES', item)
      this.submitFormHandler()
    },
    resetLevel() {
      this.$store.commit('teacher_filter/RESET_SELECTED_PROFICIENCY_LEVEL')
      this.submitFormHandler()
    },
    async resetCurrency() {
      await this.$store.dispatch('teacher_filter/resetCurrency')
      this.submitFormHandler()
    },
    resetAllClickHandler() {
      this.setActivePanel(0)
      this.$router.push({
        path: '/teacher-listing',
        params: {},
        query: {},
      })
    },
    closeTeacherFilterClickHandler() {
      this.$store.commit('SET_IS_TEACHER_FILTER', false)
    },
    submitFormHandler() {
      let params = ''

      if (this.selectedLanguage) {
        params += `language,${this.selectedLanguage.id};`
      }

      // Always include motivation in URL for proper state restoration
      if (this.selectedMotivation) {
        params += `motivation,${this.selectedMotivation.id};`
      }

      if (this.selectedSpecialities.length) {
        params += `speciality,${this.selectedSpecialities
          .map((item) => item.id)
          .join(',')};`
      }

      if (this.selectedDays.length) {
        params += `dates,${this.selectedDays.map((item) => item.id).join(',')};`
      }

      if (this.selectedTimes.length) {
        params += `time,${this.selectedTimes.map((item) => item.id).join(',')};`
      }

      if (this.selectedProficiencyLevel) {
        params += `proficiencyLevels,${this.selectedProficiencyLevel.id};`
      }

      if (
        this.selectedTeacherPreference &&
        this.selectedTeacherPreference.id !== 0
      ) {
        params += `teacherPreference,${this.selectedTeacherPreference.id};`

        if (this.selectedTeacherPreferenceLanguage) {
          params += `matchLanguages,${this.selectedTeacherPreferenceLanguage.id};`
        }
      }

      if (this.selectedFeedbackTag) {
        params += `tag,${this.selectedFeedbackTag.id};`
      }

      params += `sortOption,${
        this.selectedFeedbackTag && this.selectedSorting.isFeedbackTag
          ? 8
          : this.selectedSorting.id
      };`
      params += `currency,${this.selectedCurrency.id}`

      this.$router.push({
        path: `/teacher-listing/1/${params}`,
        query: this.searchQuery ? { search: this.searchQuery } : {},
      })
    },
  },
}
</script>
