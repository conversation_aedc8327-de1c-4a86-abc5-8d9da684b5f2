export const state = () => ({
  items: [],
  totalQuantity: 0,
})

export const mutations = {
  SET_ITEMS: (state, payload) => {
    state.items = payload
  },
  SET_TOTAL_QUANTITY: (state, payload) => {
    state.totalQuantity = payload
  },
}

export const getters = {
  totalPages: (state) =>
    Math.ceil(state.totalQuantity / process.env.NUXT_ENV_PER_PAGE),
}

export const actions = {
  getHomePageTeachers({ commit }) {
    const url = `${process.env.NUXT_ENV_API_URL}/get-publish-teachers`

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        if (Array.isArray(data)) {
          commit('SET_ITEMS', data)

          return data
        }

        return []
      })
      .catch((e) => console.log(e))
  },
  getTeachers({ commit }, { page, perPage, params, searchQuery }) {
    let url = `${process.env.NUXT_ENV_API_URL}/teacher-listing/${page}/${perPage}`

    if (params.length) {
      // Filter out motivation parameter if speciality parameter is present
      // This ensures that when specialities are selected, we only filter by speciality
      let filteredParams = params
      if (params.includes('speciality,') && params.includes('motivation,')) {
        const paramParts = params.split(';')
        filteredParams = paramParts
          .filter((part) => !part.startsWith('motivation,'))
          .join(';')
      }

      url += `/${filteredParams}`
    }

    if (searchQuery) {
      url += `;searchText,${searchQuery}`
    }

    return this.$axios
      .get(url)
      .then((response) => JSON.parse(response.data))
      .then((data) => {
        commit('SET_ITEMS', data?.teachers ?? [])
        commit('SET_TOTAL_QUANTITY', data?.countTeachers ?? 0)
      })
  },
}
